<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Wiring Minigame</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #222;
            color: white;
            font-family: Arial, sans-serif;
        }
        
        .controls {
            margin-bottom: 20px;
            padding: 20px;
            background: #333;
            border-radius: 10px;
        }
        
        button {
            padding: 10px 20px;
            margin: 5px;
            background: #0066FF;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        
        button:hover {
            background: #0055DD;
        }
        
        input, select {
            padding: 8px;
            margin: 5px;
            border: 1px solid #555;
            background: #444;
            color: white;
            border-radius: 3px;
        }
        
        label {
            display: inline-block;
            width: 120px;
            margin-right: 10px;
        }
        
        .control-group {
            margin: 10px 0;
        }
        
        #gameFrame {
            width: 100%;
            height: 600px;
            border: 2px solid #555;
            border-radius: 10px;
            background: #111;
        }
    </style>
</head>
<body>
    <h1>🔌 Test Wiring Minigame</h1>
    
    <div class="controls">
        <h3>Nastavení minihry:</h3>
        
        <div class="control-group">
            <label>Počet kabelů:</label>
            <input type="number" id="wireCount" min="1" max="32" value="4">
        </div>
        
        <div class="control-group">
            <label>Barvy:</label>
            <select id="colors">
                <option value="auto">Auto</option>
                <option value="classic" selected>Classic</option>
                <option value="extended">Extended</option>
                <option value="rainbow">Rainbow</option>
            </select>
        </div>
        
        <div class="control-group">
            <label>Snap radius:</label>
            <input type="number" id="snapRadius" min="5" max="100" value="25">
        </div>
        
        <div class="control-group">
            <label>Měřítko:</label>
            <input type="number" id="scale" min="0.1" max="2.0" step="0.1" value="1.0">
        </div>
        
        <button onclick="startMinigame()">🚀 Spustit minihru</button>
        <button onclick="stopMinigame()">⏹️ Zastavit minihru</button>
    </div>
    
    <iframe id="gameFrame" src="html/simple_enhanced.html"></iframe>
    
    <script>
        function startMinigame() {
            const wireCount = parseInt(document.getElementById('wireCount').value);
            const colors = document.getElementById('colors').value;
            const snapRadius = parseInt(document.getElementById('snapRadius').value);
            const scale = parseFloat(document.getElementById('scale').value);
            
            const gameFrame = document.getElementById('gameFrame');
            const gameWindow = gameFrame.contentWindow;
            
            // Simulace zprávy z FiveM
            const message = {
                data: {
                    ui: "ui",
                    NuiOpen: true,
                    x: "50%",
                    y: "50%",
                    scale: scale,
                    size_game: "907px",
                    name_resource: "DVRP_minigame",
                    sound_name: "1.ogg",
                    wire_count: wireCount,
                    colors: colors,
                    snap_radius: snapRadius
                }
            };
            
            gameWindow.postMessage(message.data, '*');
            console.log('Minigame started with settings:', message.data);
        }
        
        function stopMinigame() {
            const gameFrame = document.getElementById('gameFrame');
            const gameWindow = gameFrame.contentWindow;
            
            const message = {
                data: {
                    ui: "ui",
                    NuiOpen: false
                }
            };
            
            gameWindow.postMessage(message.data, '*');
            console.log('Minigame stopped');
        }
        
        // Auto-start při načtení
        window.addEventListener('load', function() {
            setTimeout(startMinigame, 1000);
        });
    </script>
</body>
</html>
