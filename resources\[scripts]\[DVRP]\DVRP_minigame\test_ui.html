<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Wiring Minigame</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #222;
            color: white;
            font-family: Arial, sans-serif;
        }
        

        
        #gameFrame {
            width: 100%;
            height: 600px;
            border: 2px solid #555;
            border-radius: 10px;
            background: #111;
        }
    </style>
</head>
<body>
    <h1>🔌 Test Wiring Minigame</h1>
    

    
    <iframe id="gameFrame" src="html/simple_enhanced.html"></iframe>
    
    <script>
        function startMinigame() {
            const wireCount = parseInt(document.getElementById('wireCount').value);
            const colors = document.getElementById('colors').value;
            const snapRadius = parseInt(document.getElementById('snapRadius').value);
            const scale = parseFloat(document.getElementById('scale').value);
            
            const gameFrame = document.getElementById('gameFrame');
            const gameWindow = gameFrame.contentWindow;
            
            // Simulace zprávy z FiveM
            const message = {
                data: {
                    ui: "ui",
                    NuiOpen: true,
                    x: "50%",
                    y: "50%",
                    scale: scale,
                    size_game: "907px",
                    name_resource: "DVRP_minigame",
                    sound_name: "1.ogg",
                    wire_count: wireCount,
                    colors: colors,
                    snap_radius: snapRadius
                }
            };
            
            gameWindow.postMessage(message.data, '*');
            console.log('Minigame started with settings:', message.data);
        }
        
        function stopMinigame() {
            const gameFrame = document.getElementById('gameFrame');
            const gameWindow = gameFrame.contentWindow;
            
            const message = {
                data: {
                    ui: "ui",
                    NuiOpen: false
                }
            };
            
            gameWindow.postMessage(message.data, '*');
            console.log('Minigame stopped');
        }
        
        // Test se spouští pouze přes tlačítko "Spustit minihru"
        // Auto-start byl odstraněn
    </script>
</body>
</html>
