<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="icon" href="favicon.ico">
    <title>Enhanced Wiring Minigame</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@100&family=Poppins:wght@100&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.11.2/css/all.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@100&display=swap" rel="stylesheet">
    
    <style>
        body {
            margin: 0;
            padding: 0;
            background: transparent;
            font-family: 'Roboto', sans-serif;
            overflow: hidden;
        }
        
        #app {
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        #container {
            position: absolute;
            user-select: none;
        }
        
        .game-svg {
            background: linear-gradient(135deg, #1e2021 0%, #272726 100%);
            border: 14px solid #000;
            border-radius: 10px;
        }
        
        .wire-drag {
            cursor: grab;
            transition: all 0.2s ease;
            stroke-width: 3px;
            filter: drop-shadow(0 0 5px rgba(0,0,0,0.7)) brightness(1.1);
        }

        .wire-drag:hover {
            filter: drop-shadow(0 0 8px rgba(0,0,0,0.8)) brightness(1.3);
            stroke-width: 4px;
        }

        .wire-drag:active {
            cursor: grabbing;
            filter: drop-shadow(0 0 10px rgba(0,0,0,0.9)) brightness(1.4);
        }

        /* Tlustší kabel při přetahování */
        .wire-drag.dragging {
            stroke-width: 6px;
            filter: drop-shadow(0 0 12px rgba(0,0,0,0.9)) brightness(1.5);
        }

        .wire-line {
            pointer-events: none;
            filter: drop-shadow(0 0 8px rgba(0,0,0,0.6));
            stroke-width: 24px;
            transition: stroke-width 0.2s ease;
        }

        /* Tlustší linie při přetahování */
        .wire-line.dragging {
            stroke-width: 32px;
            filter: drop-shadow(0 0 15px rgba(0,0,0,0.8));
        }

        /* Styly pro znaky na kabelech */
        .wire-symbol {
            font-family: 'Roboto', sans-serif;
            font-weight: bold;
            font-size: 16px;
            fill: #ffffff;
            text-anchor: middle;
            dominant-baseline: central;
            pointer-events: none;
            filter: drop-shadow(1px 1px 2px rgba(0,0,0,0.8));
        }

        .wire-symbol-target {
            font-family: 'Roboto', sans-serif;
            font-weight: bold;
            font-size: 14px;
            fill: #ffffff;
            text-anchor: middle;
            dominant-baseline: central;
            pointer-events: none;
            filter: drop-shadow(1px 1px 2px rgba(0,0,0,0.8));
        }
        
        .light {
            filter: drop-shadow(0 0 15px currentColor) drop-shadow(0 0 25px currentColor);
            transition: opacity 0.3s ease, filter 0.3s ease;
        }

        .light[opacity="1"] {
            animation: lightPulse 1.5s ease-in-out infinite alternate;
        }

        @keyframes lightPulse {
            0% { filter: drop-shadow(0 0 15px currentColor) drop-shadow(0 0 25px currentColor); }
            100% { filter: drop-shadow(0 0 20px currentColor) drop-shadow(0 0 35px currentColor) brightness(1.2); }
        }
        
        /* Styly pro target oblasti - tlustší čárkovaná čára */
        .wire-target {
            stroke-width: 5px !important;
            stroke-dasharray: 10,5 !important;
            animation: dashMove 2s linear infinite;
        }

        @keyframes dashMove {
            0% { stroke-dashoffset: 0; }
            100% { stroke-dashoffset: -15; }
        }

        /* Responsive design for different wire counts */
        .wire-container-small .wire-drag { min-height: 35px; }
        .wire-container-medium .wire-drag { min-height: 30px; }
        .wire-container-large .wire-drag { min-height: 25px; }
        .wire-container-xlarge .wire-drag { min-height: 20px; }
    </style>
</head>
<body>
    <div id="app">
        <!-- Test tlačítko pro snadné testování -->
        <div id="test-controls" style="position: fixed; top: 10px; left: 10px; z-index: 1000; background: rgba(0,0,0,0.8); padding: 10px; border-radius: 5px; display: none;">
            <button onclick="startTestGame()" style="padding: 8px 15px; margin: 3px; background: #0066FF; color: white; border: none; border-radius: 3px; cursor: pointer;">🚀 Test Start</button>
            <button onclick="stopTestGame()" style="padding: 8px 15px; margin: 3px; background: #FF3300; color: white; border: none; border-radius: 3px; cursor: pointer;">⏹️ Stop</button>
            <input type="number" id="test-wire-count" min="1" max="8" value="4" style="width: 50px; padding: 5px; margin: 3px; background: #444; color: white; border: 1px solid #666; border-radius: 3px;">
        </div>

        <div id="container" style="display: none;">
            <svg id="game-svg" width="907" height="907" viewBox="0 0 907 907" class="game-svg">
                <!-- Background gradient -->
                <defs>
                    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#1d1d1b"/>
                        <stop offset="50%" style="stop-color:#272726"/>
                        <stop offset="100%" style="stop-color:#1d1d1b"/>
                    </linearGradient>
                    
                    <filter id="wireGlow" x="-50%" y="-50%" width="200%" height="200%">
                        <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
                        <feMerge>
                            <feMergeNode in="coloredBlur"/>
                            <feMergeNode in="SourceGraphic"/>
                        </feMerge>
                    </filter>

                    <filter id="strongGlow" x="-100%" y="-100%" width="300%" height="300%">
                        <feGaussianBlur stdDeviation="6" result="coloredBlur"/>
                        <feGaussianBlur stdDeviation="3" result="coloredBlur2"/>
                        <feMerge>
                            <feMergeNode in="coloredBlur"/>
                            <feMergeNode in="coloredBlur2"/>
                            <feMergeNode in="SourceGraphic"/>
                        </feMerge>
                    </filter>
                </defs>
                
                <!-- Background -->
                <rect width="100%" height="100%" fill="url(#bgGradient)"/>
                
                <!-- Dynamic content will be inserted here -->
                <g id="dynamic-content"></g>
                
                <!-- Wire count indicator -->
                <text id="wire-count-text" x="453" y="30" text-anchor="middle" fill="#fff" font-size="16" font-family="Roboto" opacity="0.7">
                    4 Wires - Connect matching colors
                </text>
            </svg>
        </div>
    </div>

    <!-- GSAP Library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/Draggable.min.js"></script>
    <script src="js/chunk-vendors.d22fa5dc.js"></script>
    <script>
        // Enhanced mx_fixwiring with configurable wire count
        
        // Color palettes - výrazné a jasné barvy
        const COLOR_PALETTES = {
            classic: ["#0066FF", "#FF0033", "#FFDD00", "#CC00FF"],
            extended: ["#0066FF", "#FF0033", "#FFDD00", "#CC00FF", "#00FF44", "#FF6600", "#9900FF", "#00FFFF"],
            rainbow: [
                "#FF0000", "#FF4400", "#FF8800", "#FFCC00", "#FFFF00", "#CCFF00",
                "#88FF00", "#44FF00", "#00FF00", "#00FF44", "#00FF88", "#00FFCC",
                "#00FFFF", "#00CCFF", "#0088FF", "#0044FF", "#0000FF", "#4400FF",
                "#8800FF", "#CC00FF", "#FF00FF", "#FF00CC", "#FF0088", "#FF0044",
                "#CC0000", "#CC4400", "#CC8800", "#CCCC00", "#88CC00", "#44CC00",
                "#00CC00", "#00CC44", "#00CC88", "#00CCCC", "#0088CC", "#0044CC"
            ]
        };

        // Znaky pro kabely (pro barvoslepé hráče)
        const WIRE_SYMBOLS = [
            "●", "■", "▲", "♦", "★", "◆", "▼", "♠",
            "♣", "♥", "◊", "○", "□", "△", "☆", "◇",
            "♪", "♫", "※", "⚡", "⚙", "⬟", "⬢", "⬡",
            "⬠", "⬜", "⬛", "⭐", "⭕", "⚫", "⚪", "🔸"
        ];

        // Global variables
        let gameState = {
            NuiOpen: false,
            wire_count: 4,
            colors: "auto",
            snap_radius: 100, // Pevně nastaveno na 100
            wires: [],
            completedWires: [],
            draggables: [],
            name_resource: "",
            sound_name: ""
        };

        // Utility functions
        function generateAutoColors(count) {
            const colors = [];
            for (let i = 0; i < count; i++) {
                const hue = (i * 360 / count) % 360;
                const saturation = 85 + (i % 2) * 10; // Vyšší saturace pro výraznější barvy
                const lightness = 50 + (i % 3) * 5;   // Optimální světlost
                colors.push(`hsl(${hue}, ${saturation}%, ${lightness}%)`);
            }
            return colors;
        }

        function getColors(colorConfig, wireCount) {
            if (Array.isArray(colorConfig)) {
                return colorConfig.slice(0, wireCount);
            }
            
            switch (colorConfig) {
                case "classic":
                    return COLOR_PALETTES.classic.slice(0, wireCount);
                case "extended":
                    return COLOR_PALETTES.extended.slice(0, wireCount);
                case "rainbow":
                    return COLOR_PALETTES.rainbow.slice(0, wireCount);
                case "auto":
                default:
                    if (wireCount <= 4) return COLOR_PALETTES.classic.slice(0, wireCount);
                    if (wireCount <= 8) return COLOR_PALETTES.extended.slice(0, wireCount);
                    if (wireCount <= 32) return COLOR_PALETTES.rainbow.slice(0, wireCount);
                    return generateAutoColors(wireCount);
            }
        }

        function calculateWirePositions(wireCount) {
            const leftSide = [];
            const rightSide = [];
            
            const topMargin = 60;
            const bottomMargin = 60;
            const availableHeight = 907 - topMargin - bottomMargin;
            const spacing = availableHeight / (wireCount + 1);
            const wireHeight = Math.min(40, Math.max(15, availableHeight / wireCount * 0.6));
            
            for (let i = 0; i < wireCount; i++) {
                const y = topMargin + spacing * (i + 1) - wireHeight / 2;
                leftSide.push({
                    x: 60,
                    y: y,
                    width: 60,
                    height: wireHeight
                });
                rightSide.push({
                    x: 787,
                    y: y,
                    width: 108,
                    height: wireHeight
                });
            }
            
            return { leftSide, rightSide };
        }

        function generateRandomConfiguration(wireCount, colors) {
            const wires = [];
            const targetLights = [...Array(wireCount).keys()];

            // Shuffle for random connections
            for (let i = targetLights.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [targetLights[i], targetLights[j]] = [targetLights[j], targetLights[i]];
            }

            const positions = calculateWirePositions(wireCount);

            for (let i = 0; i < wireCount; i++) {
                const targetIndex = targetLights[i];
                wires.push({
                    id: i + 1,
                    color: colors[i],
                    symbol: WIRE_SYMBOLS[i % WIRE_SYMBOLS.length], // Přidání znaku
                    startPos: positions.leftSide[i],
                    targetPos: positions.rightSide[targetIndex],
                    targetLight: targetIndex + 1,
                    targetX: positions.rightSide[targetIndex].x,
                    targetY: positions.rightSide[targetIndex].y
                });
            }

            return wires;
        }

        function createSVGElement(type, attributes) {
            const element = document.createElementNS("http://www.w3.org/2000/svg", type);
            for (const [key, value] of Object.entries(attributes)) {
                element.setAttribute(key, value);
            }
            return element;
        }

        function generateSVGContent() {
            const container = document.getElementById('dynamic-content');
            container.innerHTML = '';

            // Create wire elements
            for (let i = 0; i < gameState.wire_count; i++) {
                const wire = gameState.wires[i];
                const wireId = wire.id;

                // Left connection point
                const leftConnection = createSVGElement('rect', {
                    x: wire.startPos.x - 10,
                    y: wire.startPos.y - 5,
                    width: wire.startPos.width + 20,
                    height: wire.startPos.height + 10,
                    fill: "#393e42",
                    stroke: "#555",
                    'stroke-width': "2",
                    rx: "5"
                });
                container.appendChild(leftConnection);

                // Right connection point
                const rightConnection = createSVGElement('rect', {
                    x: wire.targetPos.x - 10,
                    y: wire.targetPos.y - 5,
                    width: wire.targetPos.width + 20,
                    height: wire.targetPos.height + 10,
                    fill: "#393e42",
                    stroke: "#555",
                    'stroke-width': "2",
                    rx: "5"
                });
                container.appendChild(rightConnection);

                // Wire line - velmi tlustší kabel
                const line = createSVGElement('line', {
                    class: `wire-line line-${wireId}`,
                    x1: wire.startPos.x + wire.startPos.width / 2,
                    y1: wire.startPos.y + wire.startPos.height / 2,
                    x2: wire.startPos.x + wire.startPos.width / 2,
                    y2: wire.startPos.y + wire.startPos.height / 2,
                    stroke: wire.color,
                    'stroke-width': "24",
                    'stroke-linecap': "round",
                    filter: "url(#wireGlow)"
                });
                container.appendChild(line);

                // Target area (visual guide) - výraznější cíl s tlustší čárou
                const target = createSVGElement('rect', {
                    class: `wire-target target-${wireId}`,
                    x: wire.targetPos.x,
                    y: wire.targetPos.y,
                    width: wire.targetPos.width,
                    height: wire.targetPos.height,
                    fill: wire.color,
                    'fill-opacity': "0.3",
                    stroke: "#ffffff",
                    'stroke-width': "5",
                    'stroke-dasharray': "10,5",
                    rx: "5",
                    filter: "drop-shadow(0 0 5px " + wire.color + ")"
                });
                container.appendChild(target);

                // Symbol na target oblasti
                const targetSymbol = createSVGElement('text', {
                    class: `wire-symbol-target target-symbol-${wireId}`,
                    x: wire.targetPos.x + wire.targetPos.width / 2,
                    y: wire.targetPos.y + wire.targetPos.height / 2,
                    'font-size': "14"
                });
                targetSymbol.textContent = wire.symbol;
                container.appendChild(targetSymbol);

                // Draggable wire element - tlustší konec kabelu
                const drag = createSVGElement('rect', {
                    class: `wire-drag drag-${wireId}`,
                    x: wire.startPos.x,
                    y: wire.startPos.y,
                    width: wire.startPos.width,
                    height: wire.startPos.height,
                    fill: wire.color,
                    stroke: "#ffffff",
                    'stroke-width': "3",
                    rx: "5",
                    filter: "url(#wireGlow) drop-shadow(0 0 8px " + wire.color + ")"
                });
                container.appendChild(drag);

                // Symbol na draggable kabelu
                const dragSymbol = createSVGElement('text', {
                    class: `wire-symbol symbol-${wireId}`,
                    x: wire.startPos.x + wire.startPos.width / 2,
                    y: wire.startPos.y + wire.startPos.height / 2,
                    'font-size': "16"
                });
                dragSymbol.textContent = wire.symbol;
                container.appendChild(dragSymbol);

                // Light indicator - výraznější světlo
                const light = createSVGElement('rect', {
                    class: `light light-${wire.targetLight}`,
                    x: 847,
                    y: wire.targetPos.y + 5,
                    width: "50",
                    height: "24",
                    fill: "#00FF00",
                    opacity: "0",
                    rx: "12",
                    stroke: "#00DD00",
                    'stroke-width': "3",
                    filter: "url(#strongGlow)"
                });
                container.appendChild(light);
            }
        }

        // Game functions
        function SendToClient(endpoint, data) {
            fetch(`https://${gameState.name_resource}/${endpoint}`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(data)
            }).catch(error => console.log(`Error in ${endpoint}:`, error));
        }

        function updateLine(wireId, x2, y2) {
            const line = document.querySelector(`.line-${wireId}`);
            if (line) {
                line.setAttribute('x2', x2);
                line.setAttribute('y2', y2);
            }
        }

        function toggleLight(lightId, isOn) {
            const light = document.querySelector(`.light-${lightId}`);
            if (light) {
                if (typeof gsap !== 'undefined') {
                    gsap.to(light, {
                        opacity: isOn ? 1 : 0,
                        duration: 0.3,
                        ease: "power2.out"
                    });
                } else {
                    light.style.opacity = isOn ? 1 : 0;
                }
            }
        }

        function resetWire(wireId, wire) {
            const dragElement = document.querySelector(`.drag-${wireId}`);
            const symbolElement = document.querySelector(`.symbol-${wireId}`);
            const lineElement = document.querySelector(`.line-${wireId}`);

            if (dragElement) {
                // Odebrání třídy pro tlustší kabel
                dragElement.classList.remove('dragging');
                if (lineElement) lineElement.classList.remove('dragging');

                if (typeof gsap !== 'undefined') {
                    gsap.set(dragElement, { x: 0, y: 0 });
                } else {
                    dragElement.style.transform = 'translate(0px, 0px)';
                }
            }

            // Reset pozice symbolu
            if (symbolElement) {
                symbolElement.setAttribute('x', wire.startPos.x + wire.startPos.width / 2);
                symbolElement.setAttribute('y', wire.startPos.y + wire.startPos.height / 2);
            }

            updateLine(wireId, wire.startPos.x + wire.startPos.width / 2, wire.startPos.y + wire.startPos.height / 2);
        }

        function checkCompletion() {
            if (gameState.completedWires.every(completed => completed)) {
                // Celebrační efekt - rozsvícení všech světel
                for (let i = 1; i <= gameState.wire_count; i++) {
                    const light = document.querySelector(`.light-${i}`);
                    if (light) {
                        gsap.to(light, {
                            opacity: 1,
                            duration: 0.2,
                            ease: "power2.out",
                            yoyo: true,
                            repeat: 3
                        });
                    }
                }

                // Přehrání zvuku
                if (gameState.sound_name) {
                    new Audio(`sound/${gameState.sound_name}`).play().catch(() => {});
                }

                // Animace dokončení
                const svg = document.getElementById('game-svg');
                gsap.to(svg, {
                    boxShadow: "0 0 20px rgba(0, 255, 0, 0.7)",
                    duration: 0.5,
                    ease: "power2.out",
                    yoyo: true,
                    repeat: 1
                });

                setTimeout(() => {
                    for (let i = 0; i < gameState.wire_count; i++) {
                        resetWire(i + 1, gameState.wires[i]);
                        toggleLight(i + 1, false);
                    }
                    gameState.completedWires.fill(false);
                    SendToClient("electric_circuit_completed", {});
                }, 2000);
            }
        }

        function setupDraggables() {
            // Destroy existing draggables
            gameState.draggables.forEach(d => {
                if (d && typeof d.kill === 'function') {
                    d.kill();
                }
            });
            gameState.draggables = [];

            // Check if GSAP and Draggable are available
            if (typeof gsap === 'undefined' || typeof Draggable === 'undefined') {
                console.error('GSAP or Draggable not loaded. Wire dragging will not work.');
                return;
            }

            for (let i = 0; i < gameState.wire_count; i++) {
                const wireId = i + 1;
                const wire = gameState.wires[i];

                const draggable = Draggable.create(`.drag-${wireId}`, {
                    type: "x,y",
                    bounds: "#game-svg",
                    onDragStart: function() {
                        // Přidání třídy pro tlustší kabel při přetahování
                        const dragElement = this.target;
                        const lineElement = document.querySelector(`.line-${wireId}`);
                        const symbolElement = document.querySelector(`.symbol-${wireId}`);

                        dragElement.classList.add('dragging');
                        if (lineElement) lineElement.classList.add('dragging');
                    },
                    onDrag: function() {
                        const dragElement = this.target;
                        const symbolElement = document.querySelector(`.symbol-${wireId}`);
                        const rect = dragElement.getBoundingClientRect();
                        const svgRect = document.getElementById('game-svg').getBoundingClientRect();

                        const relativeX = rect.left - svgRect.left + rect.width / 2;
                        const relativeY = rect.top - svgRect.top + rect.height / 2;

                        // Aktualizace pozice symbolu
                        if (symbolElement) {
                            symbolElement.setAttribute('x', relativeX);
                            symbolElement.setAttribute('y', relativeY);
                        }

                        updateLine(wireId, relativeX, relativeY);
                    },

                    onRelease: function() {
                        const dragElement = this.target;
                        const lineElement = document.querySelector(`.line-${wireId}`);
                        const symbolElement = document.querySelector(`.symbol-${wireId}`);

                        // Odebrání třídy pro tlustší kabel
                        dragElement.classList.remove('dragging');
                        if (lineElement) lineElement.classList.remove('dragging');

                        const rect = dragElement.getBoundingClientRect();
                        const svgRect = document.getElementById('game-svg').getBoundingClientRect();

                        const relativeX = rect.left - svgRect.left;
                        const relativeY = rect.top - svgRect.top;

                        const targetRect = document.querySelector(`.target-${wireId}`);
                        const targetBounds = targetRect.getBoundingClientRect();
                        const targetRelativeX = targetBounds.left - svgRect.left;
                        const targetRelativeY = targetBounds.top - svgRect.top;

                        const isCorrect = Math.abs(relativeX - targetRelativeX) < gameState.snap_radius &&
                                        Math.abs(relativeY - targetRelativeY) < gameState.snap_radius;

                        if (isCorrect) {
                            // Snap to target position
                            gsap.set(dragElement, {
                                x: targetRelativeX - wire.startPos.x,
                                y: targetRelativeY - wire.startPos.y
                            });

                            // Aktualizace pozice symbolu při správném připojení
                            if (symbolElement) {
                                symbolElement.setAttribute('x', targetRelativeX + wire.targetPos.width / 2);
                                symbolElement.setAttribute('y', targetRelativeY + wire.targetPos.height / 2);
                            }

                            updateLine(wireId, targetRelativeX + wire.targetPos.width / 2,
                                     targetRelativeY + wire.targetPos.height / 2);
                            toggleLight(wire.targetLight, true);
                            gameState.completedWires[i] = true;
                            checkCompletion();
                        } else {
                            resetWire(wireId, wire);
                            toggleLight(wire.targetLight, false);
                            gameState.completedWires[i] = false;
                        }
                    }
                })[0];

                gameState.draggables.push(draggable);
            }
        }

        function initializeGame() {
            const wireColors = getColors(gameState.colors, gameState.wire_count);
            gameState.wires = generateRandomConfiguration(gameState.wire_count, wireColors);
            gameState.completedWires = new Array(gameState.wire_count).fill(false);

            // Update wire count text
            document.getElementById('wire-count-text').textContent =
                `${gameState.wire_count} Wires - Connect matching colors`;

            // Generate SVG content
            generateSVGContent();

            // Setup draggables after DOM update
            setTimeout(() => {
                setupDraggables();
            }, 100);
        }

        // Event listeners
        window.addEventListener("message", function(event) {
            if (event?.data?.ui === "ui") {
                const data = event.data;
                gameState.NuiOpen = data.NuiOpen;

                const container = document.getElementById('container');
                if (data.NuiOpen) {
                    container.style.display = 'block';
                    container.style.left = data.x;
                    container.style.top = data.y;
                    container.style.transform = `translate(-${data.x}, -${data.y}) scale(${data.scale})`;

                    const svg = document.getElementById('game-svg');
                    svg.style.width = data.size_game;

                    gameState.name_resource = data.name_resource;
                    gameState.sound_name = data.sound_name;
                    gameState.wire_count = Math.min(32, Math.max(1, data.wire_count || 4));
                    gameState.colors = data.colors || "auto";
                    gameState.snap_radius = 100; // Vždy nastaveno na 100, ignoruje data.snap_radius

                    // Add container class based on wire count
                    svg.className = 'game-svg';
                    if (gameState.wire_count <= 4) svg.classList.add('wire-container-small');
                    else if (gameState.wire_count <= 8) svg.classList.add('wire-container-medium');
                    else if (gameState.wire_count <= 16) svg.classList.add('wire-container-large');
                    else svg.classList.add('wire-container-xlarge');

                    // Fade in animation
                    container.style.opacity = '0';
                    setTimeout(() => {
                        initializeGame();
                        container.style.transition = 'opacity 0.3s ease';
                        container.style.opacity = '1';
                    }, 100);
                } else {
                    // Fade out animation
                    container.style.transition = 'opacity 0.3s ease';
                    container.style.opacity = '0';
                    setTimeout(() => {
                        container.style.display = 'none';
                    }, 300);
                }
            }
        });

        window.addEventListener("keydown", function(event) {
            if (event.key === "Escape" || event.key === "Backspace") {
                SendToClient("CloseNui", {});
            }
        });

        // Prevent context menu
        window.addEventListener("contextmenu", function(event) {
            event.preventDefault();
        });

        // Disable F12 and other dev tools shortcuts
        window.addEventListener("keydown", function(event) {
            if (event.key === "F12" ||
                (event.ctrlKey && event.shiftKey && event.key === "I") ||
                (event.ctrlKey && event.shiftKey && event.key === "C") ||
                (event.ctrlKey && event.key === "u")) {
                event.preventDefault();
            }
        });

        // Initialize GSAP when available
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof gsap !== 'undefined' && typeof Draggable !== 'undefined') {
                gsap.registerPlugin(Draggable);
                console.log('GSAP and Draggable initialized successfully');

                // Zobrazit test ovládání
                document.getElementById('test-controls').style.display = 'block';
            } else {
                console.error('GSAP or Draggable not loaded');
            }
        });

        // Testovací funkce
        function startTestGame() {
            const wireCount = parseInt(document.getElementById('test-wire-count').value) || 4;

            const message = {
                ui: "ui",
                NuiOpen: true,
                x: "50%",
                y: "50%",
                scale: 1.0,
                size_game: "907px",
                name_resource: "DVRP_minigame",
                sound_name: "1.ogg",
                wire_count: wireCount,
                colors: "classic",
                snap_radius: 100 // Vždy 100, ale bude ignorováno
            };

            window.dispatchEvent(new MessageEvent('message', { data: message }));
            console.log('Test game started with', wireCount, 'wires');
        }

        function stopTestGame() {
            const message = {
                ui: "ui",
                NuiOpen: false
            };

            window.dispatchEvent(new MessageEvent('message', { data: message }));
            console.log('Test game stopped');
        }
    </script>
</body>
</html>
